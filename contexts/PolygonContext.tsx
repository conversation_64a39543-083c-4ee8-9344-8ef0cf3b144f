import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';

// Function to calculate polygon area on a sphere using L'Huilier's Theorem for spherical excess
const calculatePolygonArea = (coordinates: { latitude: number; longitude: number }[]) => {
  if (coordinates.length < 3) {
    return 0; // Not a polygon
  }

  const R = 6371e3; // Earth's radius in meters
  const toRadians = (degrees: number) => degrees * Math.PI / 180;

  let area = 0;
  for (let i = 0; i < coordinates.length; i++) {
    const p1 = coordinates[i];
    const p2 = coordinates[(i + 1) % coordinates.length];

    const lat1 = toRadians(p1.latitude);
    const lon1 = toRadians(p1.longitude);
    const lat2 = toRadians(p2.latitude);
    const lon2 = toRadians(p2.longitude);

    // Using the formula for the area of a spherical trapezoid (approximated by <PERSON><PERSON><PERSON> on a sphere)
    // This is a simplification. For high accuracy, spherical trigonometry for each triangle in a fan is needed.
    // A more common approach for polygons on a sphere is to sum the signed areas of spherical triangles
    // formed by a reference point (e.g., the North Pole or the first vertex) and each segment of the polygon.
    area += (lon2 - lon1) * (2 + Math.sin(lat1) + Math.sin(lat2));
  }

  area = Math.abs(area * R * R / 2);

  // Convert square meters to hectares
  const squareMetersToHectares = 0.0001; // 1 hectare = 10,000 square meters
  return area * squareMetersToHectares;
};

export interface Polygon {
  id: string;
  name: string;
  coordinates: { latitude: number; longitude: number }[];
  area: number;
  color: string;
  createdAt: Date;
}

interface PolygonContextType {
  polygons: Polygon[];
  addPolygon: (coordinates: { latitude: number; longitude: number }[]) => void;
  deletePolygon: (id: string) => void;
  renamePolygon: (id: string, newName: string) => void;
  clearAllPolygons: () => void;
  useMetricUnits: boolean;
  toggleUseMetricUnits: () => void;
  mapType: 'satellite' | 'hybrid'; // Add mapType to context
  toggleMapType: () => void; // Add toggleMapType to context
  polygonToZoom: Polygon | null;
  setPolygonToZoom: (polygon: Polygon | null) => void;
  googleMapsApiKey: string | null;
  setGoogleMapsApiKey: (key: string | null) => void;
}

export const PolygonContext = createContext<PolygonContextType | undefined>(undefined);

export const usePolygons = () => {
  const context = useContext(PolygonContext);
  if (context === undefined) {
    throw new Error('usePolygons must be used within a PolygonProvider');
  }
  return context;
};

interface PolygonProviderProps {
  children: ReactNode;
}

export const PolygonProvider: React.FC<PolygonProviderProps> = ({ children }) => {
  const POLYGONS_STORAGE_KEY = '@polygons';
  const MAP_TYPE_STORAGE_KEY = '@mapType'; // Storage key for map type
  const UNIT_SYSTEM_STORAGE_KEY = '@unitSystem'; // Storage key for unit system
  const GOOGLE_MAPS_API_KEY_STORAGE_KEY = '@googleMapsApiKey'; // Storage key for Google Maps API Key

  const [polygons, setPolygons] = useState<Polygon[]>([]);
  const [useMetricUnits, setUseMetricUnits] = useState(true); // Default to metric
  const [mapType, setMapType] = useState<'satellite' | 'hybrid'>('satellite'); // Default to satellite
  const [polygonToZoom, setPolygonToZoom] = useState<Polygon | null>(null);
  const [googleMapsApiKeyInternalState, setGoogleMapsApiKeyInternal] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false); // Flag to prevent saving during initial load

  // Load settings from storage on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedPolygons = await AsyncStorage.getItem(POLYGONS_STORAGE_KEY);
        if (storedPolygons !== null) {
          const parsedPolygons = JSON.parse(storedPolygons);
          setPolygons(parsedPolygons);
          // console.debug('loadSettings:storedPolygons', parsedPolygons.length)
        } else {
          console.debug('loadSettings:storedPolygons', 'Failed')
        }

        const storedMapType = await AsyncStorage.getItem(MAP_TYPE_STORAGE_KEY) as 'satellite' | 'hybrid' | null;
        if (storedMapType) {
          setMapType(storedMapType);
          // console.debug('loadSettings:MapType',storedMapType)
        } else {
          console.debug('loadSettings:MapType','Failed')
        }

        const storedUnitSystem = await AsyncStorage.getItem(UNIT_SYSTEM_STORAGE_KEY);
        if (storedUnitSystem !== null) {
          setUseMetricUnits(JSON.parse(storedUnitSystem));
          // console.debug('loadSettings:MetricUnits',storedUnitSystem)
        } else {
          console.debug('loadSettings:MetricUnits','Failed')
        }

        const storedApiKey = await AsyncStorage.getItem(GOOGLE_MAPS_API_KEY_STORAGE_KEY);
        if (storedApiKey !== null) {
          setGoogleMapsApiKeyInternal(storedApiKey);
          // console.debug('loadSettings:storedApiKey',storedApiKey)
        } else {
          console.debug('loadSettings:ApiKey','Failed')
        }

        // Mark as initialized after all settings are loaded
        setIsInitialized(true);

      } catch (error) {
        console.error('Failed to load settings from storage', error);
        setIsInitialized(true); // Still mark as initialized even if loading fails
      }
    };
    loadSettings();
  }, []);

  // Save polygons to storage whenever they change
  useEffect(() => {
    if (!isInitialized) return; // Don't save during initial load
    
    const savePolygons = async () => {
      try {
        await AsyncStorage.setItem(POLYGONS_STORAGE_KEY, JSON.stringify(polygons));
        // console.debug('Success to save polygons to storage', polygons.length)
      } catch (error) {
        console.error('Failed to save polygons to storage', error);
      }
    };
    savePolygons();
  }, [polygons, isInitialized]);

  // Save map type to storage whenever it changes
  useEffect(() => {
    if (!isInitialized) return; // Don't save during initial load
    
    const saveMapType = async () => {
      try {
        await AsyncStorage.setItem(MAP_TYPE_STORAGE_KEY, mapType);
        // console.debug('Success to save mapType to storage')
      } catch (error) {
        console.error('Failed to save map type to storage', error);
      }
    };
    saveMapType();
  }, [mapType, isInitialized]);

  // Save unit system to storage whenever it changes
  useEffect(() => {
    if (!isInitialized) return; // Don't save during initial load
    
    const saveUnitSystem = async () => {
      try {
        await AsyncStorage.setItem(UNIT_SYSTEM_STORAGE_KEY, JSON.stringify(useMetricUnits));
        // console.debug('Success to save useMetricUnits to storage')
      } catch (error) {
        console.error('Failed to save unit system to storage', error);
      }
    };
    saveUnitSystem();
  }, [useMetricUnits, isInitialized]);

  // Save Google Maps API Key to storage whenever it changes
  useEffect(() => {
    const saveApiKey = async () => {
      try {
        if (googleMapsApiKeyInternalState !== null) {
          await AsyncStorage.setItem(GOOGLE_MAPS_API_KEY_STORAGE_KEY, googleMapsApiKeyInternalState);
          // console.debug('Success to save GOOGLE_MAPS_API_KEY_STORAGE_KEY to storage')
        } else {
          await AsyncStorage.removeItem(GOOGLE_MAPS_API_KEY_STORAGE_KEY);
          console.debug('Success to remove GOOGLE_MAPS_API_KEY_STORAGE_KEY to storage')
        }
      } catch (error) {
        console.error('Failed to save Google Maps API Key to storage', error);
      }
    };
    if (!isInitialized) return; // Don't save during initial load
    
    saveApiKey();
  }, [googleMapsApiKeyInternalState, isInitialized]);

  const setGoogleMapsApiKey = (key: string | null) => {
    setGoogleMapsApiKeyInternal(key);
  };

  const addPolygon = (coordinates: { latitude: number; longitude: number }[]) => {
    const area = calculatePolygonArea(coordinates);
    const newPolygon: Polygon = {
      id: Date.now().toString(),
      name: `Polygon ${polygons.length + 1}`,
      coordinates,
      area,
      color: '#4CAF50',
      createdAt: new Date(),
    };
    setPolygons(prev => [...prev, newPolygon]);
  };

  const deletePolygon = (id: string) => {
    setPolygons(prev => prev.filter(polygon => polygon.id !== id));
  };

  const renamePolygon = (id: string, newName: string) => {
    setPolygons(prev => 
      prev.map(polygon => 
        polygon.id === id ? { ...polygon, name: newName } : polygon
      )
    );
  };

  const clearAllPolygons = () => {
    setPolygons([]);
  };

  const toggleUseMetricUnits = () => {
    setUseMetricUnits(prev => !prev);
  };

  const toggleMapType = () => {
    setMapType(prev => prev === 'satellite' ? 'hybrid' : 'satellite');
  };

  const value: PolygonContextType = {
    polygons,
    addPolygon,
    deletePolygon,
    renamePolygon,
    clearAllPolygons,
    useMetricUnits,
    toggleUseMetricUnits,
    mapType,
    toggleMapType,
    polygonToZoom,
    setPolygonToZoom,
    googleMapsApiKey: googleMapsApiKeyInternalState,
    setGoogleMapsApiKey,
  };

  return (
    <PolygonContext.Provider value={value}>
      {children}
    </PolygonContext.Provider>
  );
};
