# Building an APK for Your Expo Project

This guide outlines the steps to build an APK (Android Package Kit) for your Expo project. This process allows you to create a standalone Android application that can be installed on Android devices.

## Prerequisites

Before you begin, ensure you have the following installed and configured:

1.  **Node.js and npm/yarn:** Expo CLI and other dependencies require Node.js.
2.  **Expo CLI:** If you haven't already, install Expo CLI globally:
    ```bash
    npm install -g expo-cli
    # or
    yarn global add expo-cli
    ```
3.  **EAS CLI (Expo Application Services CLI):** EAS Build is the recommended way to build your app for production.
    ```bash
    npm install -g eas-cli
    # or
    yarn global add eas-cli
    ```
4.  **An Expo Account:** You'll need an Expo account to use EAS Build. If you don't have one, create it at [https://expo.dev/signup](https://expo.dev/signup).
5.  **Logged into Expo CLI and EAS CLI:**
    ```bash
    expo login
    eas login
    ```

## Building the APK using EAS Build

EAS Build is Expo's cloud build service that simplifies the process of building and signing your app.

### 1. Configure Your Project for EAS Build

If you haven't already, configure your project to use EAS Build:

```bash
cd /home/<USER>/code/mepond/mepond-app
eas build:configure
```

This command will create or update an `eas.json` file in your project root. This file contains build profiles (e.g., `development`, `preview`, `production`). For building an APK, you'll typically use a `production` or a custom profile.

Ensure your `eas.json` has an Android build profile. It might look something like this:

```json
{
  "build": {
    "production": {
      "android": {
        "buildType": "apk"
      }
    },
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      }
    }
  }
}
```

**Note:** The `"buildType": "apk"` line is crucial for generating an APK. By default, EAS Build might generate an AAB (Android App Bundle) for production builds, which is the format required by Google Play. If you specifically need an APK for direct distribution or testing, ensure this is set.

### 2. Start the Build

To build an APK for Android, run the following command from your project root (`/home/<USER>/code/mepond/mepond-app`):

```bash
# For a production APK (if configured in eas.json)
eas build -p android --profile production

# Or, if you have a specific profile for APKs, e.g., 'apk_build_profile'
# eas build -p android --profile apk_build_profile
```

*   `-p android` specifies that you want to build for the Android platform.
*   `--profile production` (or your chosen profile) tells EAS Build which build profile to use from your `eas.json`.

EAS CLI will then upload your project to the Expo build servers, and the build process will begin. You'll see a link to the build details page on the Expo website where you can monitor the progress.

### 3. Download the APK

Once the build is complete, you can download the APK in a few ways:

*   **From the Build Details Page:** The link provided in your terminal will take you to a page where you can download the APK directly.
*   **Using EAS CLI:** You can list recent builds and download the artifact:
    ```bash
    eas build:list
    ```
    Find your build ID from the list, then:
    ```bash
    # Replace YOUR_BUILD_ID with the actual ID
    eas build:download YOUR_BUILD_ID
    ```
    Or, more directly, if it was the latest build for the `production` profile:
    ```bash
    eas build:download --platform android --profile production --latest
    ```

## Important Considerations

*   **App Signing:** For production APKs, EAS Build handles app signing. You can let Expo manage your keystore, or you can provide your own existing keystore.
*   **`app.json` Configuration:** Ensure your `app.json` (or `app.config.js`/`app.config.ts`) is correctly configured, especially fields like `expo.android.package`, `expo.version`, `expo.android.versionCode`, `expo.name`, `expo.icon`, etc.
*   **Build Profiles:** Customize build profiles in `eas.json` to suit different needs (e.g., development builds with debug symbols, release builds for different environments).
*   **Credentials:** EAS Build manages your Android Keystore and other credentials securely.

This guide provides the standard steps for building an APK with EAS Build. Refer to the official [Expo EAS Build documentation](https://docs.expo.dev/build/introduction/) for more detailed information and advanced configurations.