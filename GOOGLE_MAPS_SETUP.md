# Google Maps API 配置指南

## 概述
您的应用已经配置为使用Google Maps API并显示卫星视图。要完成设置，您需要获取Google Maps API密钥并进行配置。

## 已完成的修改

### 1. 应用配置 (app.json)
- 添加了Google Maps API密钥配置
- 添加了必要的Android权限：
  - `ACCESS_FINE_LOCATION`
  - `ACCESS_COARSE_LOCATION`

### 2. 地图组件 (app/(tabs)/index.tsx)
- 移除了第三方地图瓦片 (UrlTile)
- 配置MapView使用Google Maps提供商
- 设置地图类型为卫星视图 (`mapType="satellite"`)
- 添加了 `PROVIDER_GOOGLE` 导入

## 需要您完成的步骤

### 1. 获取Google Maps API密钥

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用以下API：
   - Maps SDK for Android
   - Maps JavaScript API (如果需要Web支持)
4. 创建API密钥：
   - 转到 "APIs & Services" > "Credentials"
   - 点击 "Create Credentials" > "API Key"
   - 复制生成的API密钥

### 2. 配置API密钥

在 `app.json` 文件中，将 `YOUR_GOOGLE_MAPS_API_KEY` 替换为您的实际API密钥：

```json
"android": {
  "config": {
    "googleMaps": {
      "apiKey": "您的实际API密钥"
    }
  }
}
```

### 3. 安全配置（推荐）

为了安全起见，建议限制API密钥的使用：

1. 在Google Cloud Console中，编辑您的API密钥
2. 在 "Application restrictions" 下选择 "Android apps"
3. 添加您的应用包名和SHA-1证书指纹

### 4. 测试配置

1. 重新构建应用：`expo run:android`
2. 确保地图显示为卫星视图
3. 验证用户位置和多边形绘制功能正常工作

## 功能特性

现在您的应用具有以下功能：
- ✅ Google Maps卫星视图
- ✅ 用户位置显示
- ✅ 多边形绘制功能
- ✅ 地图交互（缩放、平移）

## 故障排除

如果遇到问题：

1. **地图不显示**：检查API密钥是否正确配置
2. **权限错误**：确保已授予位置权限
3. **构建错误**：尝试清理缓存：`expo start --clear`

## 费用说明

Google Maps API是付费服务，但提供免费配额。请查看 [Google Maps Platform定价](https://cloud.google.com/maps-platform/pricing) 了解详情。
