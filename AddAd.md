# 在 Polygons 页面集成 Google AdMob 原生广告指南

本文档旨在指导您如何在 Mepond 应用的 Polygons 页面集成 Google AdMob 原生广告。

## 目录

1.  [前提条件](#1-前提条件)
2.  [集成 Google AdMob](#2-集成-google-admob)
    2.1. [创建 AdMob 账户并注册应用](#21-创建-admob-账户并注册应用)
    2.2. [创建广告单元](#22-创建广告单元)
    2.3. [安装 AdMob 库](#23-安装-admob-库)
    2.4. [配置 Android 项目](#24-配置-android-项目)
    2.5. [在 Polygons 页面显示条幅广告](#25-在-polygons-页面显示条幅广告)
3.  [测试广告](#3-测试广告)
4.  [注意事项](#4-注意事项)

## 1. 前提条件

-   您的应用已在 Google Play Console 中设置（即使尚未发布）。
-   您拥有一个 Google AdMob 账户。
-   您的开发环境已配置好 React Native (Expo)。

## 2. 集格 Google AdMob

### 2.1. 创建 AdMob 账户并注册应用

1.  访问 [Google AdMob](https://admob.google.com/) 并使用您的 Google 账户登录或注册。
2.  在 AdMob 中添加您的应用：
    -   点击 “应用” > “添加应用”。
    -   选择平台 “Android”。
    -   如果您的应用已在 Google Play 上发布，请搜索并选择它。如果尚未发布，请选择 “否”，然后输入应用名称并点击 “添加”。
    -   AdMob 会为您的应用生成一个 **应用 ID (App ID)**。请妥善保管此 ID，稍后需要将其添加到您的应用配置中。

### 2.2. 创建广告单元

广告单元代表您应用中展示广告的位置。

1.  在 AdMob 中，选择您的应用。
2.  点击 “广告单元” > “添加广告单元”。
3.  选择广告格式 “原生” (Native) -> “原生高级广告”。
4.  为广告单元命名 (例如 `polygons_native_ad`)。
5.  根据您的应用设计和需求配置原生广告的模板和设置。
6.  点击 “创建广告单元”。
7.  AdMob 会为该广告单元生成一个 **广告单元 ID (Ad Unit ID)**。请妥善保管此 ID，稍后在代码中使用它来请求广告。

### 2.3. 安装 AdMob 库

对于 Expo 项目，推荐使用 `react-native-google-mobile-ads`。

-   **卸载旧库 (如果之前安装过):**
    ```bash
    npm uninstall expo-ads-admob
    # 或者 yarn remove expo-ads-admob
    ```
-   **安装新库:**
    ```bash
    npm install react-native-google-mobile-ads
    # 或者 yarn add react-native-google-mobile-ads
    ```
-   **安装完成后，需要重新构建应用开发版本:**
    ```bash
    eas build -p android --profile development --local
    ```

### 2.4. 配置 Android 项目

1.  **在 `app.json` 中配置 AdMob 应用 ID:**
    打开您的 `app.json` (或 `app.config.js`) 文件，在根级别添加 `react-native-google-mobile-ads` 对象，并配置您的 AdMob 应用 ID。

    ```jsonc
    // app.json
    {
      "expo": {
        "name": "Your App Name",
        "slug": "your-app-slug",
        // ... 其他 expo 相关配置
        "android": {
          // ... 其他 android 配置
        }
      },
      "react-native-google-mobile-ads": {
        "android_app_id": "ca-app-pub-xxxxxxxxxxxxxxxx~yyyyyyyyyy" // 替换为您的 AdMob 应用 ID
      }
    }
    ```

2.  **构建应用:**
    由于 `react-native-google-mobile-ads` 包含原生代码，您需要为您的应用创建一个开发版本才能进行测试。
    ```bash
    # 如果尚未构建或需要更新原生依赖，请运行：
    eas build -p android --profile development --local
    ```
    然后通过 `expo start --dev-client` 启动开发版本。

### 2.5. 在 Polygons 页面显示原生广告

现在您可以在 `polygons.tsx` 文件中集成原生广告组件。原生广告的集成比条幅广告更复杂，因为它允许您自定义广告的外观和感觉，使其与应用内容融为一体。

打开 `app/(tabs)/polygons.tsx` 文件并进行修改。以下是一个集成原生广告的示例框架，您需要根据 AdMob 后台配置的原生广告模板和您应用的设计来具体实现广告的渲染逻辑。

```tsx
import React, { useEffect, useState, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Platform,
  FlatList, // 假设您的 Polygon 列表使用 FlatList
  // ... 其他您需要的导入
} from 'react-native';
import mobileAds, {
  NativeAdEventType,
  TestIds,
  AdEventType,
  NativeAd,
  NativeAdView,
  ImageView,
  HeadlineView,
  TaglineView,
  AdvertiserView,
  StarRatingView,
  // 根据您的原生广告模板，可能还需要导入其他视图组件
} from 'react-native-google-mobile-ads';

// 替换为您的 AdMob 原生广告单元 ID
const nativeAdUnitID = __DEV__
  ? TestIds.NATIVE // 开发时使用测试 ID
  : Platform.OS === 'ios'
  ? 'YOUR_IOS_NATIVE_AD_UNIT_ID' // 替换为您的 iOS 原生广告单元 ID
  : 'YOUR_ANDROID_NATIVE_AD_UNIT_ID'; // 替换为您的 Android 原生广告单元 ID

// 假设这是您的 Polygon 数据类型
interface PolygonItem {
  id: string;
  name: string;
  // ... 其他属性
}

// 广告占位符类型
interface AdPlaceholder {
  isAd: true;
}

type ListItem = PolygonItem | AdPlaceholder;

export default function PolygonsScreen() {
  const [polygons, setPolygons] = useState<PolygonItem[]>([]); // 您的多边形数据
  const [listData, setListData] = useState<ListItem[]>([]);
  const [adLoaded, setAdLoaded] = useState(false);
  const [adError, setAdError] = useState<Error | null>(null);
  const nativeAdRef = useRef<NativeAd>(null);

  useEffect(() => {
    // 初始化 AdMob SDK
    mobileAds()
      .initialize()
      .then(adapterStatuses => {
        console.log('AdMob SDK initialized:', adapterStatuses);
        loadNativeAd();
      })
      .catch(error => console.warn('AdMob SDK initialization error', error));

    // 模拟加载多边形数据
    const mockPolygons: PolygonItem[] = [
      { id: '1', name: 'Polygon 1' },
      { id: '2', name: 'Polygon 2' },
      { id: '3', name: 'Polygon 3' },
      { id: '4', name: 'Polygon 4' },
      { id: '5', name: 'Polygon 5' },
    ];
    setPolygons(mockPolygons);

    return () => {
      // 组件卸载时销毁广告
      if (nativeAdRef.current) {
        nativeAdRef.current.destroy();
      }
    };
  }, []);

  useEffect(() => {
    // 当多边形数据或广告加载状态变化时，重新构建列表数据
    const newListData: ListItem[] = [...polygons];
    if (adLoaded && nativeAdRef.current) {
      const adIndex = Math.min(3, newListData.length);
      newListData.splice(adIndex, 0, { isAd: true });
    }
    setListData(newListData);
  }, [polygons, adLoaded]);

  const loadNativeAd = () => {
    setAdLoaded(false);
    setAdError(null);

    if (nativeAdRef.current) {
        nativeAdRef.current.destroy();
    }

    const ad = NativeAd.createForAdRequest(nativeAdUnitID, {
      requestNonPersonalizedAdsOnly: true, // 根据用户隐私设置
      // 其他请求选项
    });

    nativeAdRef.current = ad;

    const unsubscribeLoaded = ad.addAdEventListener(NativeAdEventType.LOADED, () => {
      console.log('Native Ad loaded');
      setAdLoaded(true);
      setAdError(null);
    });

    const unsubscribeError = ad.addAdEventListener(AdEventType.ERROR, (error) => {
      console.error('Native Ad failed to load', error);
      setAdError(error);
      setAdLoaded(false);
    });
    
    // 开始加载广告
    ad.load();

    // 返回取消订阅函数，以便在组件卸载或重新加载广告时清理
    return () => {
      unsubscribeLoaded();
      unsubscribeError();
      if (nativeAdRef.current) {
        nativeAdRef.current.destroy();
      }
    };
  };

  const renderListItem = ({ item }: { item: ListItem }) => {
    if ('isAd' in item && item.isAd) {
      if (!adLoaded || !nativeAdRef.current) {
        return null; // 或者显示一个加载占位符
      }
      // 在这里构建您的原生广告视图
      // 您需要根据 AdMob 后台配置的原生广告模板来设计这个视图
      return (
        <NativeAdView
          ref={nativeAdRef.current} // 将 NativeAd 实例传递给 NativeAdView
          style={styles.nativeAdView}
        >
          <View style={styles.adContainer}>
            <ImageView style={styles.adImage} />
            <View style={styles.adInfoContainer}>
              <HeadlineView style={styles.adHeadline} />
              <TaglineView numberOfLines={1} style={styles.adTagline} />
              <AdvertiserView style={styles.adAdvertiser} />
              <StarRatingView style={styles.adStarRating} />
            </View>
            {/* 根据需要添加其他广告元素，例如 CallToActionView */}
          </View>
        </NativeAdView>
      );
    }

    // 渲染您的 Polygon 列表项
    return (
      <View style={styles.polygonItem}>
        <Text>{item.name}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Polygons Page</Text>
      {adError && <Text style={styles.errorText}>Ad Error: {adError.message}</Text>}
      <FlatList
        data={listData}
        renderItem={renderListItem}
        keyExtractor={(item) => ('isAd' in item ? 'ad' : item.id)}
        // ... 其他 FlatList 属性
      />
      {/* 如果需要在列表外重新加载广告，可以添加一个按钮 */}
      {/* <Button title="Reload Ad" onPress={loadNativeAd} /> */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // alignItems: 'center', // 根据您的布局调整
    paddingTop: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  polygonItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginVertical: 10,
  },
  // 原生广告样式 (请根据您的设计调整)
  nativeAdView: {
    width: '100%',
    alignSelf: 'center',
    // 根据您的广告设计调整高度或让其自适应
    // height: 300, 
    marginVertical: 10,
    paddingHorizontal: 10,
  },
  adContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    backgroundColor: '#f9f9f9',
    // flexDirection: 'row', // 或 'column'，取决于您的布局
  },
  adImage: {
    width: '100%',
    height: 150, // 示例高度
    marginBottom: 10,
  },
  adInfoContainer: {
    // flex: 1, // 如果与图片并排显示
  },
  adHeadline: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
  },
  adTagline: {
    fontSize: 12,
    color: '#555',
  },
  adAdvertiser: {
    fontSize: 10,
    color: '#888',
    marginTop: 5,
  },
  adStarRating: {
    // StarRatingView 可能需要特定样式
    marginTop: 5,
  },
});
```

**代码说明:**

-   导入 `react-native-google-mobile-ads` 中的相关组件，特别是 `NativeAd`, `NativeAdView` 以及用于展示广告素材的各种视图组件 (如 `ImageView`, `HeadlineView` 等)。
-   定义 `nativeAdUnitID`。**重要:** 在开发和测试期间，请使用 Google 提供的测试广告单元 ID (`TestIds.NATIVE`)。在发布应用前，务必将其替换为您在 AdMob 中创建的真实原生广告单元 ID。
-   使用 `useEffect` 在组件加载时初始化 AdMob SDK 并加载原生广告。
-   `loadNativeAd` 函数负责创建 `NativeAd` 实例，并监听加载成功和失败事件。
-   通过 `nativeAdRef.current = ad;` 将 `NativeAd` 实例的引用保存起来，以便在 `NativeAdView` 中使用。
-   `listData` 状态用于管理包含多边形数据和广告占位符的列表。
-   `renderListItem` 函数根据列表项的类型（是普通数据还是广告）来渲染不同的内容。
    -   如果项是广告 (`'isAd' in item && item.isAd`)，则渲染 `<NativeAdView>`。**您必须将 `nativeAdRef.current` (即加载的 `NativeAd` 实例) 传递给 `NativeAdView` 的 `ref` 属性。**
    -   在 `<NativeAdView>` 内部，您需要根据您在 AdMob 后台配置的原生广告模板来布局各个广告元素 (如图片、标题、描述等)，并使用 `react-native-google-mobile-ads` 提供的相应视图组件 (如 `<ImageView>`, `<HeadlineView>`) 来展示它们。
-   **原生广告的布局和样式 (`styles.nativeAdView`, `styles.adContainer` 等) 需要您根据自己的应用设计和 AdMob 中定义的模板仔细调整。**
-   确保在组件卸载时调用 `nativeAdRef.current.destroy()` 来销毁广告对象，避免内存泄漏。
-   广告的插入逻辑是：在多边形列表的第3项之后插入广告，如果列表项少于3项，则在列表末尾插入。

## 3. 测试广告

-   **使用测试 ID:** 始终使用测试广告单元 ID 和测试设备进行开发，以避免产生无效点击。
-   **检查日志:** 查看控制台输出，确认广告是否成功加载或是否有错误信息。
-   **AdMob 控制台:** 在 AdMob 控制台中监控广告单元的表现（尽管测试广告不会产生真实收入）。

## 4. 注意事项

-   **用户体验:**
    -   不要在用户不期望的地方或以干扰性的方式展示广告。
    -   确保广告不会遮挡应用的核心功能或内容。
    -   遵守 AdMob 的政策，例如不要鼓励用户点击广告。
-   **隐私:**
    -   如果您的应用收集用户数据用于个性化广告，请确保遵守相关的隐私法规 (如 GDPR, CCPA)，并在应用中提供清晰的隐私政策。
    -   `servePersonalizedAds` 属性可以控制是否投放个性化广告。
-   **错误处理:** 妥善处理广告加载失败的情况，避免应用崩溃或用户体验不佳。
-   **发布前替换 ID:** 在将应用发布到 Google Play Store 之前，务必将所有测试广告单元 ID 替换为您的真实 AdMob 广告单元 ID。
-   **审核:** Google Play 会审核应用中的广告实现，确保其符合政策。

通过以上步骤，您应该能够在 Mepond 应用的 Polygons 页面成功集成 Google AdMob 原生广告。请务必仔细阅读并遵循 Google AdMob 关于原生广告的政策和指南，确保您的广告实现合规。