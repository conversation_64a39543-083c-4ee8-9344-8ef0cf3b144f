import React, { useEffect, useState } from 'react';
import { Platform, Text, View } from 'react-native';
import {
  NativeAd,
  NativeAdView,
  NativeAsset,
  NativeAssetType,
  NativeMediaView,
  TestIds
} from 'react-native-google-mobile-ads';

// Test ad unit ID for development - replace with real ID for production
const nativeAdUnitID = __DEV__
  ? TestIds.NATIVE
  : Platform.OS === 'ios'
  ? 'ca-app-pub-3940256099942544/3986624511' // Replace with your iOS native ad unit ID
  : 'ca-app-pub-3940256099942544/2247696110'; // Your provided test ID

interface NativeAdComponentProps {
  style?: any;
}

export default function NativeAdComponent({ style }: NativeAdComponentProps) {
  const [nativeAd, setNativeAd] = useState<NativeAd | null>(null);
  const [adError, setAdError] = useState<Error | null>(null);

  useEffect(() => {
    loadNativeAd();

    return () => {
      // Cleanup ad on component unmount
      if (nativeAd) {
        nativeAd.destroy();
      }
    };
  }, []);

  const loadNativeAd = () => {
    setAdError(null);

    // Destroy existing ad if any
    if (nativeAd) {
      nativeAd.destroy();
    }

    NativeAd.createForAdRequest(nativeAdUnitID)
      .then((ad) => {
        console.log('Native Ad loaded successfully');
        setNativeAd(ad);
        setAdError(null);
      })
      .catch((error) => {
        console.error('Native Ad failed to load:', error);
        setAdError(error);
        setNativeAd(null);
      });
  };

  // Don't render anything if ad hasn't loaded or there's an error
  if (!nativeAd || adError) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <NativeAdView nativeAd={nativeAd} style={styles.nativeAdView}>
        <View style={styles.adContainer}>
          <View style={styles.adHeader}>
            <Text style={styles.adLabel}>Ad</Text>
          </View>

          <View style={styles.adContent}>
            <NativeMediaView style={styles.adImage} />

            <View style={styles.adInfo}>
              {nativeAd.headline && (
                <NativeAsset assetType={NativeAssetType.HEADLINE}>
                  <Text style={styles.adHeadline}>{nativeAd.headline}</Text>
                </NativeAsset>
              )}

              {nativeAd.body && (
                <NativeAsset assetType={NativeAssetType.BODY}>
                  <Text numberOfLines={2} style={styles.adTagline}>
                    {nativeAd.body}
                  </Text>
                </NativeAsset>
              )}

              <View style={styles.adFooter}>
                {nativeAd.advertiser && (
                  <NativeAsset assetType={NativeAssetType.ADVERTISER}>
                    <Text style={styles.adAdvertiser}>{nativeAd.advertiser}</Text>
                  </NativeAsset>
                )}

                {nativeAd.starRating && (
                  <NativeAsset assetType={NativeAssetType.STAR_RATING}>
                    <Text style={styles.adStarRating}>
                      ⭐ {nativeAd.starRating.toFixed(1)}
                    </Text>
                  </NativeAsset>
                )}
              </View>

              {nativeAd.callToAction && (
                <NativeAsset assetType={NativeAssetType.CALL_TO_ACTION}>
                  <View style={styles.adCallToAction}>
                    <Text style={styles.adCallToActionText}>
                      {nativeAd.callToAction}
                    </Text>
                  </View>
                </NativeAsset>
              )}
            </View>
          </View>
        </View>
      </NativeAdView>
    </View>
  );
}

const styles = {
  container: {
    marginVertical: 8,
    marginHorizontal: 16,
  },
  nativeAdView: {
    width: '100%' as const,
  },
  adContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  adHeader: {
    marginBottom: 8,
  },
  adLabel: {
    fontSize: 10,
    color: '#999',
    fontWeight: 'bold' as const,
    textTransform: 'uppercase' as const,
  },
  adContent: {
    flexDirection: 'row' as const,
  },
  adImage: {
    width: 80,
    height: 80,
    borderRadius: 6,
    marginRight: 12,
    backgroundColor: '#f5f5f5',
  },
  adInfo: {
    flex: 1,
    justifyContent: 'space-between' as const,
  },
  adHeadline: {
    fontSize: 16,
    fontWeight: 'bold' as const,
    color: '#333',
    marginBottom: 4,
  },
  adTagline: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 8,
  },
  adFooter: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  adAdvertiser: {
    fontSize: 12,
    color: '#888',
    marginRight: 8,
  },
  adStarRating: {
    fontSize: 12,
    color: '#888',
  },
  adCallToAction: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    alignSelf: 'flex-start' as const,
  },
  adCallToActionText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold' as const,
  },
};