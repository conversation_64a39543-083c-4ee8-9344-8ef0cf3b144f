import React from 'react';
import { Platform, Text, View } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';

// Test ad unit ID for development - replace with real ID for production
const bannerAdUnitID = __DEV__
  ? TestIds.ADAPTIVE_BANNER // Use ADAPTIVE_BANNER for version 14.7.2
  : Platform.OS === 'android'
  ? 'ca-app-pub-8377651725703584/8902514184'
  : 'ca-app-pub-3940256099942544/2247696110'; // Your provided test ID

interface NativeAdComponentProps {
  style?: any;
}

export default function NativeAdComponent({ style }: NativeAdComponentProps) {
  const handleAdFailedToLoad = (error: any) => {
    console.warn('Banner Ad failed to load:', error);
  };

  const handleAdLoaded = () => {
    console.log('Banner Ad loaded successfully');
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.adContainer}>
        <View style={styles.adHeader}>
          <Text style={styles.adLabel}>Ad</Text>
        </View>

        <BannerAd
          unitId={bannerAdUnitID}
          size={BannerAdSize.BANNER}
          requestOptions={{
            requestNonPersonalizedAdsOnly: true,
          }}
          onAdLoaded={handleAdLoaded}
          onAdFailedToLoad={handleAdFailedToLoad}
        />
      </View>
    </View>
  );
}

const styles = {
  container: {
    marginVertical: 8,
    marginHorizontal: 8,
  },
  nativeAdView: {
    width: '100%' as const,
  },
  adContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 6,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  adHeader: {
    marginBottom: 8,
  },
  adLabel: {
    fontSize: 10,
    color: '#999',
    fontWeight: 'bold' as const,
    textTransform: 'uppercase' as const,
  },
  adContent: {
    flexDirection: 'row' as const,
  },
  adImage: {
    width: 80,
    height: 80,
    borderRadius: 6,
    marginRight: 12,
    backgroundColor: '#f5f5f5',
  },
  adInfo: {
    flex: 1,
    justifyContent: 'space-between' as const,
  },
  adHeadline: {
    fontSize: 16,
    fontWeight: 'bold' as const,
    color: '#333',
    marginBottom: 4,
  },
  adTagline: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 8,
  },
  adFooter: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  adAdvertiser: {
    fontSize: 12,
    color: '#888',
    marginRight: 8,
  },
  adStarRating: {
    fontSize: 12,
    color: '#888',
  },
  adCallToAction: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    alignSelf: 'flex-start' as const,
  },
  adCallToActionText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold' as const,
  },
};