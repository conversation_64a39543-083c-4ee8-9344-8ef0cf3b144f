# 发布应用到 Google Play 商店及实现付费分享功能指南

本文档旨在指导您如何将 Mepond 应用发布到 Google Play 商店，并实现付费用户才能分享 Polygon 的功能。

## 目录

1.  [发布到 Google Play 商店](#1-发布到-google-play-商店)
    1.1. [准备工作](#11-准备工作)
    1.2. [创建 Google Play Developer 账户](#12-创建-google-play-developer-账户)
    1.3. [准备 App Store 素材](#13-准备-app-store-素材)
    1.4. [构建和签名应用](#14-构建和签名应用)
    1.5. [配置 Google Play Console](#15-配置-google-play-console)
    1.6. [上传和发布](#16-上传和发布)
2.  [实现付费分享 Polygon 功能](#2-实现付费分享-polygon-功能)
    2.1. [选择和集成应用内购买 (IAP) 库](#21-选择和集成应用内购买-iap-库)
    2.2. [在 Google Play Console 中配置商品](#22-在-google-play-console-中配置商品)
    2.3. [实现购买逻辑](#23-实现购买逻辑)
    2.4. [根据购买状态控制分享功能](#24-根据购买状态控制分享功能)
3.  [相关 NPM 包](#3-相关-npm-包)

## 1. 发布到 Google Play 商店

### 1.1. 准备工作

-   确保您的应用符合 Google Play 的政策。
-   准备好应用的最终版本代码。
-   拥有一个 Google 账户。

### 1.2. 创建 Google Play Developer 账户

-   访问 [Google Play Console](https://play.google.com/console)。
-   使用您的 Google 账户登录或注册。
-   同意开发者协议并支付一次性的注册费用。

### 1.3. 准备 App Store 素材

您需要准备以下素材：

-   **应用名称 (App Name):** 最多 50 个字符。
-   **简短说明 (Short Description):** 最多 80 个字符。
-   **完整说明 (Full Description):** 最多 4000 个字符。
-   **高分辨率应用图标 (High-resolution App Icon):** 512x512 像素，32 位 PNG (含 Alpha 通道)。
-   **置顶大图 (Feature Graphic):** 1024x500 像素，JPEG 或 24 位 PNG (无 Alpha 通道)。
-   **屏幕截图 (Screenshots):** 至少 2 张，最多 8 张 (JPEG 或 24 位 PNG，无 Alpha 通道)。支持多种设备尺寸 (手机、7 英寸平板电脑、10 英寸平板电脑)。
-   **宣传视频 (Promo Video) (可选):** YouTube 视频链接。

### 1.4. 构建和签名应用

对于 React Native (Expo) 应用：

-   **生成 Android App Bundle (AAB) 或 APK:**
    ```bash
    # 确保已登录 Expo 账户
    expo login

    # 构建 AAB (推荐)
    eas build -p android --profile preview
    # 或者构建 APK
    # eas build -p android --profile preview --artifact-path ./build.apk
    ```
    您可能需要根据 `eas.json` 中的配置调整 `profile`。通常会有一个 `production` 或 `release` 配置用于发布。

-   **应用签名:** Expo (EAS Build) 会自动处理应用签名。您需要在首次构建时生成或上传签名密钥。
    -   **生成新的签名密钥 (推荐):** EAS Build 可以为您生成并管理签名密钥。
    -   **上传现有的签名密钥:** 如果您有现有的密钥，也可以上传。
    详细信息请参考 [Expo 文档关于应用签名的部分](https://docs.expo.dev/app-signing/introduction/)。

### 1.5. 配置 Google Play Console

1.  **创建应用:**
    -   在 Google Play Console 中，点击 “创建应用”。
    -   填写应用详细信息，如应用名称、默认语言、应用类型（应用或游戏）、免费或付费。
    -   同意声明和政策。
2.  **设置商品详情 (Store Listing):**
    -   填写之前准备好的应用名称、简短说明、完整说明。
    -   上传应用图标、置顶大图和屏幕截图。
    -   选择应用类别和标签。
    -   提供联系方式和隐私政策网址。
3.  **应用内容 (App Content):**
    -   完成所有必要的问卷，如隐私政策、广告、应用访问权限、内容分级、目标受众和内容、数据安全等。
4.  **发布 (Release):
    -   **选择测试轨道 (可选但推荐):**
        -   内部测试 (Internal testing)
        -   封闭式测试 (Closed testing)
        -   开放式测试 (Open testing)
    -   **创建正式版 (Production Release):**
        -   导航到 “正式版” (Production) 部分。
        -   点击 “创建新版本” (Create new release)。
        -   上传您的 AAB 或 APK 文件。
        -   输入版本名称和版本说明。
        -   检查错误和警告。
5.  **定价和分发 (Pricing & Distribution):**
    -   设置应用是免费还是付费。
    -   选择分发国家/地区。
    -   确认应用是否包含广告，以及是否符合美国出口法律。

### 1.6. 上传和发布

-   在 “发布概览” (Publishing overview) 页面，检查所有步骤是否完成。
-   点击 “发送以供审核” (Send for review)。
-   Google Play 会审核您的应用，这可能需要几天时间。审核通过后，您的应用将在所选国家/地区的 Google Play 商店上架。

## 2. 实现付费分享 Polygon 功能

要实现付费用户才能分享 Polygon 的功能，您需要集成应用内购买 (In-App Purchases, IAP)。

### 2.1. 选择和集成应用内购买 (IAP) 库

对于 React Native 应用，`react-native-iap` 是一个流行的选择。

-   **安装:**
    ```bash
    npm install react-native-iap
    # 或者
    yarn add react-native-iap
    ```
    对于 Expo 项目，如果使用 EAS Build，原生模块会自动链接。如果使用 Expo Go，则可能需要自定义开发客户端或 eject。

-   **配置:** 根据 `react-native-iap` 的文档进行 Android 平台的特定配置 (通常涉及 `AndroidManifest.xml` 和 `build.gradle`，但 EAS Build 可能会简化此过程)。

### 2.2. 在 Google Play Console 中配置商品

1.  **添加结算库依赖 (如果需要手动配置):**
    确保您的 `android/app/build.gradle` 文件中包含 Google Play Billing Library 的依赖：
    ```gradle
    dependencies {
        // ... 其他依赖
        implementation 'com.android.billingclient:billing:6.0.1' // 使用最新版本
    }
    ```
    *注意: `react-native-iap` 通常会处理此依赖。请检查其文档。*

2.  **创建应用内商品 (In-app products):**
    -   在 Google Play Console 中，选择您的应用。
    -   导航到 “创收” > “商品” (Monetize > Products)。
    -   点击 “创建商品” (Create product)。
    -   **商品 ID (Product ID):** 设置一个唯一的 ID (例如 `polygon_sharing_unlock` 或 `premium_features`)。这个 ID 将在您的代码中使用。
    -   **类型 (Type):** 选择 “受管理的商品” (Managed product) (对于一次性购买解锁功能) 或 “订阅” (Subscription) (如果希望用户定期付费)。对于解锁分享功能，通常选择 “受管理的商品”。
    -   **名称和说明:** 填写用户可见的商品名称和说明。
    -   **定价:** 设置商品价格。
    -   激活商品。

### 2.3. 实现购买逻辑

使用 `react-native-iap` 提供的 API 来处理购买流程：

1.  **初始化连接:** 在应用启动时连接到 Play Store。
    ```javascript
    import { InAppPurchase, PurchaseError, SubscriptionPurchase, acknowledgePurchaseAndroid, consumePurchaseAndroid, finishTransaction, getProducts, initConnection, requestPurchase, purchaseErrorListener, purchaseUpdatedListener } from 'react-native-iap';

    // ...

    useEffect(() => {
      const initIap = async () => {
        try {
          await initConnection();
          // 对于 Android，如果存在未完成的购买，可能需要处理
          // await flushFailedPurchasesCachedAsPendingAndroid();
        } catch (error) {
          console.warn('Error initializing IAP:', error);
        }
      };
      initIap();

      const purchaseUpdateSubscription = purchaseUpdatedListener(async (purchase) => {
        const receipt = purchase.transactionReceipt;
        if (receipt) {
          try {
            // 在 Android 上，对于非消耗品，您可能需要确认购买
            if (Platform.OS === 'android' && purchase.purchaseToken) {
              await acknowledgePurchaseAndroid({ token: purchase.purchaseToken });
            }
            // 购买成功，解锁功能
            // await finishTransaction({purchase, isConsumable: false}); // 根据商品类型决定是否消耗
            console.log('Purchase successful:', purchase);
            // 更新用户状态，例如保存到 AsyncStorage 或后端
            // setUserHasPremium(true);
          } catch (ackErr) {
            console.warn('Error acknowledging purchase:', ackErr);
          }
        }
      });

      const purchaseErrorSubscription = purchaseErrorListener((error) => {
        console.warn('Purchase error:', error);
      });

      return () => {
        purchaseUpdateSubscription.remove();
        purchaseErrorSubscription.remove();
        // endConnection(); // 考虑在应用关闭时断开连接
      };
    }, []);
    ```

2.  **获取商品信息:**
    ```javascript
    const productIds = ['polygon_sharing_unlock']; // 替换为您的商品 ID

    const fetchProducts = async () => {
      try {
        const products = await getProducts({skus: productIds});
        console.log('Products:', products);
        // 保存商品信息以供显示
        // setAvailableProducts(products);
      } catch (err) {
        console.warn('Error fetching products:', err);
      }
    };
    ```

3.  **发起购买请求:**
    ```javascript
    const handlePurchase = async (sku) => {
      try {
        await requestPurchase({sku});
      } catch (err) {
        console.warn('Error requesting purchase:', err.message);
      }
    };
    ```

4.  **恢复购买 (Restore Purchases):**
    允许用户在重新安装应用或更换设备后恢复已购买的非消耗品。
    ```javascript
    const getAvailablePurchases = async () => {
      try {
        const purchases = await getAvailablePurchases();
        if (purchases && purchases.length > 0) {
          // 检查购买记录，解锁相应功能
          purchases.forEach(purchase => {
            if (purchase.productId === 'polygon_sharing_unlock') {
              // Unlock feature
              // setUserHasPremium(true);
            }
          });
        }
      } catch (error) {
        console.warn('Error getting available purchases:', error);
      }
    };
    ```

### 2.4. 根据购买状态控制分享功能

-   在您的应用中维护一个状态 (例如，使用 React Context, Redux, Zustand 或 AsyncStorage) 来跟踪用户是否已购买付费功能。
-   当用户尝试分享 Polygon 时，检查此状态。
-   如果用户未购买：
    -   禁用分享按钮或显示提示信息。
    -   提供一个购买选项，点击后调用 `handlePurchase` 方法。
-   如果用户已购买：
    -   启用分享功能。

**示例逻辑:**

```javascript
// 假设您有一个状态 `userHasPremium`

const SharePolygonButton = () => {
  const [userHasPremium, setUserHasPremium] = useState(false); // 从存储或 Context 获取
  const productIdToBuy = 'polygon_sharing_unlock';

  // ... (useEffect to check purchase status on load)

  const onSharePress = () => {
    if (userHasPremium) {
      // 执行分享操作
      console.log('Sharing polygon...');
    } else {
      // 提示用户购买
      Alert.alert(
        'Unlock Sharing',
        'To share polygons, please purchase the premium feature.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Buy', onPress: () => handlePurchase(productIdToBuy) },
        ]
      );
    }
  };

  return <Button title="Share Polygon" onPress={onSharePress} />;
};
```

**重要注意事项:**

-   **安全性:** 对于关键的付费功能，建议在后端验证购买凭证 (receipt validation) 以防止欺诈。`react-native-iap` 提供了购买凭证，您可以将其发送到您的服务器进行验证。
-   **测试:** Google Play Console 提供了测试应用内购买的工具，包括测试账户和许可测试人员。
-   **用户体验:** 清晰地告知用户哪些功能是付费的，并提供流畅的购买和恢复体验。

## 3. 相关 NPM 包

-   `react-native-iap`: 用于实现应用内购买功能。

请根据您的具体需求和应用架构调整上述步骤和代码示例。