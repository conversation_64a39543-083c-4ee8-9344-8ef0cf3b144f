<svg width="128" height="128" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#B3E5FC;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#81D4FA;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影效果 -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="1"/>
      <feOffset dx="1" dy="1.5" result="offsetblur"/>
      <feFlood flood-color="#000000" flood-opacity="0.2"/>
      <feComposite in2="offsetblur" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- 网点图案 -->
    <pattern id="gridPattern" width="10" height="10" patternUnits="userSpaceOnUse">
      <circle cx="1.5" cy="1.5" r="0.6" fill="#4FC3F7" fill-opacity="0.4"/>
    </pattern>
  </defs>

  <!-- 背景层 -->
  <rect x="4" y="4" width="92" height="92" rx="12" ry="12" fill="url(#bgGradient)" stroke="#4A90E2" stroke-width="0.8"/>
  
  <!-- 网点层 -->
  <rect x="4" y="4" width="92" height="92" rx="12" ry="12" fill="url(#gridPattern)" />

  <!-- 主要形状 (橙色多边形) -->
  <polygon points="30,35 30,65 40,63 40,45 50,55 60,45 60,63 70,65 70,35 60,34 50,42 40,34" 
           fill="#FF7043" stroke="#E64A19" stroke-width="1.5" filter="url(#dropShadow)"
           fill-opacity="0.9"/>

  <!-- 控制点 (白色圆圈，红色边框) -->
  <circle cx="30" cy="65" r="3.5" fill="#FFFFFF" stroke="#E53935" stroke-width="1.5"/>
  <circle cx="30" cy="35" r="3.5" fill="#FFFFFF" stroke="#E53935" stroke-width="1.5"/>
  <circle cx="50" cy="55" r="3.5" fill="#FFFFFF" stroke="#E53935" stroke-width="1.5"/>
  <circle cx="70" cy="35" r="3.5" fill="#FFFFFF" stroke="#E53935" stroke-width="1.5"/>
  <circle cx="70" cy="65" r="3.5" fill="#FFFFFF" stroke="#E53935" stroke-width="1.5"/>

  <!-- 特殊控制点 (右上角带X的) -->
  <!-- <g transform="translate(80 18)"> 
    <circle cx="0" cy="0" r="4.5" fill="#FFFFFF" stroke="#1976D2" stroke-width="1.5"/>
    <line x1="-2.5" y1="-2.5" x2="2.5" y2="2.5" stroke="#1976D2" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="-2.5" y1="2.5" x2="2.5" y2="-2.5" stroke="#1976D2" stroke-width="1.5" stroke-linecap="round"/>
  </g> -->
</svg>