{"expo": {"name": "mepond", "slug": "mepond-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/app_icon_1024.png", "scheme": "mepondapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true}, "android": {"permissions": ["INTERNET", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"], "adaptiveIcon": {"foregroundImage": "./assets/images/app_icon_1024.png", "backgroundColor": "#ffffff"}, "config": {"googleMaps": {"apiKey": "AIzaSyBWLN5c5DGcC80p-rECRWjiFP1we5kkNbk"}}, "package": "com.singlewinghx.mepondapp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/app_icon.svg"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/app_icon_1024.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-8377651725703584~8240229273"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "3fb38af3-76eb-4c2b-adf0-e5cf9678189e"}}, "fonts": ["./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/AntDesign.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Entypo.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/EvilIcons.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Feather.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Fontisto.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Foundation.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Ionicons.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialIcons.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Octicons.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/SimpleLineIcons.ttf", "./node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Zocial.ttf"]}}