import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, Keyboard, Modal, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import MapView, { Marker, Polygon, Polyline, PROVIDER_GOOGLE } from 'react-native-maps'; // Polyline is correctly imported here
import { SafeAreaView } from 'react-native-safe-area-context';
import { Polygon as PolygonType, usePolygons } from '../../contexts/PolygonContext';

// Helper function to calculate distance between two coordinates (Haversine formula)
const getDistance = (coord1: { latitude: number; longitude: number }, coord2: { latitude: number; longitude: number }, useMetricUnits: boolean) => {
  const R = 6371e3; // Earth radius in meters
  const lat1 = coord1.latitude * Math.PI / 180;
  const lat2 = coord2.latitude * Math.PI / 180;
  const deltaLat = (coord2.latitude - coord1.latitude) * Math.PI / 180;
  const deltaLon = (coord2.longitude - coord1.longitude) * Math.PI / 180;

  const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
            Math.cos(lat1) * Math.cos(lat2) *
            Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distanceInMeters = R * c; // Distance in meters

  if (useMetricUnits) {
    return `${distanceInMeters.toFixed(0)} m`;
  } else {
    const distanceInFeet = distanceInMeters * 3.28084; // Convert meters to feet
    return `${distanceInFeet.toFixed(0)} ft`;
  }
};

// Helper function to calculate polygon centroid
const getPolygonCenter = (coordinates: { latitude: number; longitude: number }[]) => {
  if (coordinates.length === 0) return { latitude: 0, longitude: 0 };
  
  let latSum = 0;
  let lonSum = 0;
  
  coordinates.forEach(coord => {
    latSum += coord.latitude;
    lonSum += coord.longitude;
  });
  
  return {
    latitude: latSum / coordinates.length,
    longitude: lonSum / coordinates.length
  };
};

// Helper function to format area display
const formatArea = (area: number, useMetricUnits: boolean) => {
  if (useMetricUnits) {
    return `${area.toFixed(2)} ha`;
  } else {
    return `${(area * 2.47105).toFixed(2)} acres`;
  }
};

export default function MapScreen() {
  const { polygons: savedPolygons, addPolygon, renamePolygon, useMetricUnits, mapType, polygonToZoom, setPolygonToZoom, googleMapsApiKey } = usePolygons(); // Get mapType and googleMapsApiKey from context
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<{ coordinates: { latitude: number; longitude: number }[] } | null>(null);
  const [overlayVisible, setOverlayVisible] = useState(false);
  const [currentPolygon, setCurrentPolygon] = useState<{ latitude: number; longitude: number }[]>([]);
  const [isDrawingMode, setIsDrawingMode] = useState(false);
  const [selectedVertexIndex, setSelectedVertexIndex] = useState<number | null>(null);
  const [selectedPolygonForInfo, setSelectedPolygonForInfo] = useState<PolygonType | null>(null);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newPolygonName, setNewPolygonName] = useState('');
  const mapRef = useRef<MapView>(null);
  const [zoomLevel, setZoomLevel] = useState(15); // Initial zoom level, adjust as needed
  const ZOOM_THRESHOLD = 14; // Threshold to hide labels when zoomed out

  const [isSearchModalVisible, setIsSearchModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState(''); // This might be removed or repurposed

  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg('Permission to access location was denied');
        return;
      }

      let location = await Location.getCurrentPositionAsync({});
      setLocation(location);
    })();
  }, []);

  useEffect(() => {
    if (polygonToZoom) {
      if (mapRef.current && polygonToZoom.coordinates.length > 0) {
        mapRef.current.fitToCoordinates(polygonToZoom.coordinates, {
          edgePadding: {
            top: 50,
            right: 50,
            bottom: 50,
            left: 50,
          },
          animated: true,
        });
      }
      setPolygonToZoom(null); // Reset after zooming
    }
  }, [polygonToZoom]);

  const handleMapPress = (event: { nativeEvent: { coordinate: { latitude: number; longitude: number } } }) => {
    if (isDrawingMode) {
      const { coordinate } = event.nativeEvent;
      if (selectedVertexIndex !== null) {
        const updatedPolygon = [...currentPolygon];
        updatedPolygon[selectedVertexIndex] = coordinate;
        setCurrentPolygon(updatedPolygon);
        setSelectedVertexIndex(null); 
      } else {
        setCurrentPolygon([...currentPolygon, coordinate]);
      }
    } else if (selectedPolygonForInfo) {
      // setSelectedPolygonForInfo(null); 
    }
  };

  const handleSearchPress = () => {
    if (!googleMapsApiKey) {
      Alert.alert('API Key Required', 'Please set your Google Maps API Key in the settings to use the search feature.');
      return;
    }
    // Potentially validate API key here before showing search modal or directly show it
    // For now, we assume if key exists, it might be valid, actual validation happens on API call
    setIsSearchModalVisible(true);
  };

  const undoLastPoint = () => {
    if (currentPolygon.length > 0) {
      setCurrentPolygon(currentPolygon.slice(0, -1));
      if (selectedVertexIndex === currentPolygon.length -1) {
        setSelectedVertexIndex(null);
      }
    }
  };

  const completePolygon = () => {
    if (currentPolygon.length >= 3) {
      addPolygon(currentPolygon);
      setCurrentPolygon([]);
      setIsDrawingMode(false);
      Alert.alert('Polygon created', 'Polygon has been created and saved successfully');
    } else {
      Alert.alert('Error', 'A polygon needs at least 3 points');
    }
  };

  const cancelPolygon = () => {
    setCurrentPolygon([]);
    setIsDrawingMode(false);
    setSelectedVertexIndex(null);
  };

  const handlePolygonPressInternal = (polygon: PolygonType) => {
    setSelectedPolygonForInfo(polygon);
  };

  const handleRenamePolygon = () => {
    if (selectedPolygonForInfo && newPolygonName.trim() !== '') {
      renamePolygon(selectedPolygonForInfo.id, newPolygonName.trim());
      setSelectedPolygonForInfo(prev => prev ? { ...prev, name: newPolygonName.trim() } : null);
      setIsRenameModalVisible(false);
      setNewPolygonName(''); 
    } else {
      Alert.alert('Error', 'Polygon name cannot be empty.');
    }
  };

  // Updated handleSearch to use data from GooglePlacesAutocomplete
  const handlePlaceSelect = (data: any, details: any = null) => {
    Keyboard.dismiss();
    if (details?.geometry?.location) {
      const { lat, lng } = details.geometry.location;
      mapRef.current?.animateToRegion({
        latitude: lat,
        longitude: lng,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
      setIsSearchModalVisible(false);
    } else {
      // Fallback for coordinate search if details are not available (e.g., direct coordinate input)
      // This part might need adjustment based on how GooglePlacesAutocomplete handles direct coordinate input
      // or if we keep a separate input for coordinates.
      const coordsParts = data.description.split(',').map((part: string) => parseFloat(part.trim()));
      if (coordsParts.length === 2 && !isNaN(coordsParts[0]) && !isNaN(coordsParts[1])) {
        const [latitude, longitude] = coordsParts;
        if (latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180) {
          mapRef.current?.animateToRegion({
            latitude,
            longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });
          setIsSearchModalVisible(false);
          return;
        }
      }
      Alert.alert('Not Found', 'Location details not found. Please select a valid place.');
    }
  };

  const handlePlaceFail = (error: any) => {
    Alert.alert('Failed', `Caused by: ${error || ''}`)
  }

  let initialRegion = {
    latitude: -6.8504,  
    longitude: 109.1368,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  };

  if (location) {
    initialRegion = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    };
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />

      {errorMsg ? (
        <Text style={styles.errorText}>{errorMsg}</Text>
      ) : (
        <View style={{ flex: 1 }}>
          <MapView
            ref={mapRef}
            style={styles.map}
            initialRegion={initialRegion}
            onPress={handleMapPress}
            showsUserLocation={true}
            showsMyLocationButton={true} // Keep this true for native map controls
            mapType={mapType} // Use mapType from context
            provider={PROVIDER_GOOGLE}
            onRegionChangeComplete={(region) => {
              // Calculate zoom level based on latitudeDelta
              // This is an approximation, you might need a more precise formula
              const newZoomLevel = Math.log2(360 / region.latitudeDelta);
              setZoomLevel(newZoomLevel);
            }}
          >
            {location && (
              <Marker
                coordinate={{
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude,
                }}
                title="Your Location"
              />
            )}

            {selectedPhoto && overlayVisible && (
              <Polygon
                coordinates={selectedPhoto.coordinates}
                fillColor="rgba(255, 0, 0, 0.2)"
                strokeColor="rgba(255, 0, 0, 0.8)"
                strokeWidth={2}
              />
            )}

            {savedPolygons.map((polygon) => (
              <React.Fragment key={`polygon-fragment-${polygon.id}`}>
                <Polygon
                  key={`polygon-${polygon.id}`}
                  coordinates={polygon.coordinates}
                  fillColor="rgba(0, 255, 0, 0.2)"
                  strokeColor="rgba(0, 255, 0, 0.8)"
                  strokeWidth={2}
                  tappable
                  onPress={() => handlePolygonPressInternal(polygon)}
                />
                {/* Area label at polygon center - only show if zoomLevel is high enough */}
                {/* {zoomLevel > ZOOM_THRESHOLD && (
                  <Marker
                    key={`area-label-${polygon.id}`}
                    coordinate={getPolygonCenter(polygon.coordinates)}
                    anchor={{ x: 0.5, y: 0.5 }} // Center the anchor point
                  >
                    <View style={styles.areaLabelContainer}>
                      <Text style={styles.areaLabel}>{formatArea(polygon.area, useMetricUnits)}</Text>
                    </View>
                  </Marker>
                )} */}
                {/* Line segment length labels */}
                {polygon.coordinates.map((point, index) => {
                  if (index === 0) return null;
                  const prevPoint = polygon.coordinates[index - 1];
                  const distance = getDistance(prevPoint, point, useMetricUnits);
                  const midPoint = {
                    latitude: (prevPoint.latitude + point.latitude) / 2,
                    longitude: (prevPoint.longitude + point.longitude) / 2,
                  };

                  return (
                    <React.Fragment key={`saved-line-dist-${polygon.id}-${index}`}>
                      <Polyline
                        coordinates={[prevPoint, point]}
                        strokeColor="rgba(0, 255, 0, 0.8)"
                        strokeWidth={2}
                      />
                      {/* Line length label - only show if zoomLevel is high enough */}
                      {zoomLevel > ZOOM_THRESHOLD && (
                        <Marker
                          coordinate={midPoint}
                          anchor={{ x: 0.5, y: 0.5 }}
                        >
                          <View style={styles.distanceLabelContainer}>
                            <Text style={styles.distanceLabel}>{distance}</Text>
                          </View>
                        </Marker>
                      )}
                    </React.Fragment>
                  );

                })}
                {/* Closing segment from last point to first point */}
                {polygon.coordinates.length > 2 && (() => {
                  const lastPoint = polygon.coordinates[polygon.coordinates.length - 1];
                  const firstPoint = polygon.coordinates[0];
                  const distance = getDistance(lastPoint, firstPoint, useMetricUnits);
                  const midPoint = {
                    latitude: (lastPoint.latitude + firstPoint.latitude) / 2,
                    longitude: (lastPoint.longitude + firstPoint.longitude) / 2,
                  };

                  return (
                    <React.Fragment key={`saved-closing-line-${polygon.id}`}>
                      <Polyline
                        coordinates={[lastPoint, firstPoint]}
                        strokeColor="rgba(0, 255, 0, 0.8)"
                        strokeWidth={2}
                      />
                      {/* Closing line length label - only show if zoomLevel is high enough */}
                      {zoomLevel > ZOOM_THRESHOLD && (
                        <Marker
                          coordinate={midPoint}
                          anchor={{ x: 0.5, y: 0.5 }}
                        >
                          <View style={styles.distanceLabelContainer}>
                            <Text style={styles.distanceLabel}>{distance}</Text>
                          </View>
                        </Marker>
                      )}
                    </React.Fragment>
                  );

                })()}
              </React.Fragment>
            ))}

            {currentPolygon.length > 0 && (
              <Polygon // This is the polygon being drawn
                coordinates={currentPolygon}
                fillColor="rgba(0, 0, 255, 0.2)"
                strokeColor="rgba(0, 0, 255, 0.8)"
                strokeWidth={2}
              />
            )}

            {isDrawingMode && currentPolygon.length > 1 && currentPolygon.map((point, index) => {
              if (index === 0) return null; 
              const prevPoint = currentPolygon[index - 1];
              const distance = getDistance(prevPoint, point, useMetricUnits);
              const midPoint = {
                latitude: (prevPoint.latitude + point.latitude) / 2,
                longitude: (prevPoint.longitude + point.longitude) / 2,
              };

              return (
                <React.Fragment key={`line-dist-${index}`}>
                  <Polyline // Polyline for the segment itself
                    coordinates={[prevPoint, point]}
                    strokeColor="rgba(0, 0, 255, 0.8)" 
                    strokeWidth={2} // Make sure this is not causing issues, it's a valid prop
                  />
                  {/* Current drawing line length label - only show if zoomLevel is high enough */}
                  {zoomLevel > ZOOM_THRESHOLD && (
                    <Marker
                      coordinate={midPoint}
                      anchor={{ x: 0.5, y: 0.5 }} 
                    >
                      <View style={styles.distanceLabelContainer}>
                        <Text style={styles.distanceLabel}>{distance}</Text>
                      </View>
                    </Marker>
                  )}
                </React.Fragment>
              );

            })}

            {currentPolygon.map((point, index) => (
              <Marker
                key={`current-point-${index}`}
                coordinate={point}
                draggable
                onDragEnd={(e) => {
                  const updatedPolygon = [...currentPolygon];
                  updatedPolygon[index] = e.nativeEvent.coordinate;
                  setCurrentPolygon(updatedPolygon);
                }}
                onPress={() => {
                  if (selectedVertexIndex === index) {
                    setSelectedVertexIndex(null);
                  } else {
                    setSelectedVertexIndex(index);
                  }
                }}
                pinColor={selectedVertexIndex === index ? 'blue' : 'red'} 
              >
                <View style={styles.polygonPoint}><Text style={styles.pointNumber}>{index + 1}</Text></View>
              </Marker>
            ))}
          </MapView>

          {/* Drawing Actions (Undo, Complete, Cancel) - Visible only in drawing mode */}
          {isDrawingMode && (
            <View style={styles.drawingActionsContainer}>
              <TouchableOpacity style={styles.controlButton} onPress={undoLastPoint}>
                <MaterialCommunityIcons name="undo" size={24} color="white" />
                <Text style={styles.buttonText}>Undo</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.controlButton} onPress={completePolygon}>
                <MaterialCommunityIcons name="check" size={24} color="white" />
                <Text style={styles.buttonText}>Complete</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.controlButton, styles.cancelButton]} onPress={cancelPolygon}>
                <MaterialCommunityIcons name="close" size={24} color="white" />
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Floating Action Buttons (Draw and Search) - Visible when NOT in drawing mode */}
          {!isDrawingMode && (
            <>
              {googleMapsApiKey && (
                <TouchableOpacity style={styles.searchButton} onPress={handleSearchPress}>
                  <MaterialCommunityIcons name="magnify" size={28} color="white" />
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.drawButton} // Draw button on the right
                onPress={() => setIsDrawingMode(true)}
              >
                <MaterialCommunityIcons name="pencil-outline" size={28} color="white" />
              </TouchableOpacity>
            </>
          )}
        </View>
      )}

      {/* Polygon Info Modal */}
      {selectedPolygonForInfo && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={!!selectedPolygonForInfo}
          onRequestClose={() => {
            setSelectedPolygonForInfo(null);
            setIsRenameModalVisible(false); 
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalView}>
              <Text style={styles.modalTitle}>{selectedPolygonForInfo.name}</Text>
                  <Text style={styles.modalText}>
                    Area: {useMetricUnits 
                      ? `${selectedPolygonForInfo.area.toFixed(2)} ha` 
                      : `${(selectedPolygonForInfo.area * 2.47105).toFixed(2)} acres`}
                  </Text>
                  <Text style={styles.modalText}>Coordinates: {selectedPolygonForInfo.coordinates.length}</Text>
              
              {isRenameModalVisible ? (
                <>
                  <TextInput
                    style={styles.renameInput}
                    onChangeText={setNewPolygonName}
                    value={newPolygonName}
                    placeholder="Enter new polygon name"
                  />
                  <View style={styles.renameButtonsContainer}>
                    <TouchableOpacity
                      style={styles.modalButton}
                      onPress={handleRenamePolygon}
                    >
                      <Text style={styles.modalButtonText}>Save Name</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.modalButton, styles.modalCloseButton]}
                      onPress={() => setIsRenameModalVisible(false)}
                    >
                      <Text style={styles.modalButtonText}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                </>
              ) : (
                <TouchableOpacity
                  style={styles.modalButton}
                  onPress={() => {
                    setNewPolygonName(selectedPolygonForInfo.name); 
                    setIsRenameModalVisible(true);
                  }}
                >
                  <Text style={styles.modalButtonText}>Rename</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[styles.modalButton, styles.modalCloseButton, {marginTop: isRenameModalVisible ? 10 : 20}] }
                onPress={() => setSelectedPolygonForInfo(null)}
              >
                <Text style={styles.modalButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      )}

      <Modal
        animationType="slide"
        transparent={true}
        visible={isSearchModalVisible}
        onRequestClose={() => {
          setIsSearchModalVisible(false);
        }}
      >
        <View style={styles.modalContainer}> 
          <View style={[styles.modalView, styles.searchModalView]}> 
            <Text style={styles.modalTitle}>Search Location</Text>
            <GooglePlacesAutocomplete
              placeholder='Enter location name or lat,lon'
              onPress={handlePlaceSelect}
              onFail={handlePlaceFail}
              query={{
                key: googleMapsApiKey || '', // Use the API key from context, ensure it's not null
                language: 'en', // or your preferred language
              }}
              fetchDetails={true}
              styles={{
                textInputContainer: styles.placesTextInputContainer,
                textInput: styles.placesTextInput,
                listView: styles.placesListView,
                poweredContainer: styles.placesPoweredContainer, // Hide "powered by Google"
              }}
              debounce={400} // debounce the requests in ms. Set to 0 to remove debounce. By default 0ms.
              numberOfLines={1} // Limit the number of lines in the input field
              isRowScrollable={true}
              // renderRow={(rowData) => <Text>{rowData.description}</Text>} // Optional: Custom row rendering
              // predefinedPlaces={[]}
              // currentLocation={true} // Will add a 'Current location' button at the top of the predefined places list
              // currentLocationLabel="Current location"
              // nearbyPlacesAPI='GooglePlacesSearch' // Which API to use: GoogleReverseGeocoding or GooglePlacesSearch
              // GoogleReverseGeocodingQuery={{ // available options for GoogleReverseGeocoding API : https://developers.google.com/maps/documentation/geocoding/intro
              //   // ...
              // }}
              // GooglePlacesSearchQuery={{ // available options for GooglePlacesSearch API : https://developers.google.com/maps/documentation/places/web-service/search
              //   rankby: 'distance',
              //   type: 'cafe'
              // }}
              // GooglePlacesDetailsQuery={{ // available options for GooglePlacesDetails API : https://developers.google.com/maps/documentation/places/web-service/details
              //   fields: 'formatted_address,geometry,name',
              // }}
              // filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}
              // predefinedPlacesAlwaysVisible={false}
              // enablePoweredByContainer={false} // Hide "powered by Google"
              // minLength={2} // minimum length of text to search
              // listViewDisplayed='auto'    // true/false/undefined
              // returnKeyType={'search'} // Can be left out for default keyboard behavior
              // renderDescription={row => row.description} // custom description render
              // onNotFound={() => console.log('no results')}
              // listEmptyComponent={() => (
              //   <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
              //     <Text>No results were found</Text>
              //   </View>
              // )}
              // listUnderlayColor={'#c8c7cc'}
              // textInputProps={{
              //   InputComp: TextInput,
              //   leftIcon: { type: 'font-awesome', name: 'chevron-left' },
              //   errorStyle: { color: 'red' }
              // }}
            />
            <TouchableOpacity
                style={[styles.modalButton, styles.modalCancelButton, {marginTop: 20}]} 
                onPress={() => {
                  setIsSearchModalVisible(false);
                }}
              >
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
          </View>
        </View>
      </Modal>

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#fff', // Removed to let map take full background
  },
  map: {
    flex: 1, // Ensure map takes full space of its container
    // width: '100%', // Redundant if flex: 1 is on parent View
    // height: '100%', // Redundant if flex: 1 is on parent View
  },
  errorText: {
    flex: 1,
    textAlign: 'center',
    textAlignVertical: 'center', // For Android
    fontSize: 18,
    padding: 20,
  },
  // Styles for Floating Action Buttons (Draw and Search)
  drawButton: { // FAB for starting drawing mode - on the RIGHT
    position: 'absolute',
    bottom: 20, 
    right: 20,
    backgroundColor: '#2196F3', // Blue for draw
    padding: 15,
    borderRadius: 30, // Circular button
    elevation: 5,
    zIndex: 1000, // High zIndex to be on top
  },
  searchButton: { // FAB for search - on the LEFT
    position: 'absolute',
    bottom: 20, 
    left: 20,
    backgroundColor: '#4CAF50', // Green for search
    padding: 15,
    borderRadius: 30, // Circular button
    elevation: 5,
    zIndex: 1000, // High zIndex to be on top
  },
  // Styles for drawing action buttons (Undo, Complete, Cancel) when isDrawingMode is true
  drawingActionsContainer: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'column',
    gap: 8,
  },
  controlButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  // ... (other styles: polygonPoint, distanceLabel, modals, inputs, etc. remain largely the same)
  buttonText: {
    color: 'white',
    marginLeft: 4,
    fontWeight: 'bold',
  },  
  polygonPoint: {
    width: 20, // Slightly smaller for less clutter
    height: 20,
    borderRadius: 10,
    backgroundColor: '#2196F3',
    borderWidth: 1,
    borderColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pointNumber: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  distanceLabelContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 3,
    paddingVertical: 3,
    borderRadius: 3,
  },
  distanceLabel: {
    fontSize: 9,
    color: '#000000',
    fontWeight: 'normal', // Changed to normal for thinner font
  },
  areaLabelContainer: {
    backgroundColor: 'rgba(166, 238, 166, 0.9)', // Slightly more opaque
    paddingHorizontal: 2, // Increased padding
    paddingVertical: 5,   // Increased padding
    borderRadius: 6,      // Larger radius
    borderWidth: 1,       // Thicker border
    borderColor: 'rgba(0, 200, 0, 0.9)', // Darker green border
  },
  areaLabel: {
    fontSize: 10,         // Larger font size
    color: '#000000',
    fontWeight: 'bold',
    textAlign: 'center',  // Center align text
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-start', // Align modal to top for search results
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingTop: 50, // Add padding to avoid status bar overlap
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 15, // Soften modal corners
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '90%', // Wider modal for search results
  },
  searchModalView: {
    height: '80%', // Allow more height for search results
  },
  modalTitle: {
    fontSize: 22, // Larger title
    fontWeight: '600', // Bolder
    marginBottom: 20, // More space below title
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 10,
    textAlign: 'left', // Align text to left for readability
    fontSize: 16,
    width: '100%', // Ensure text uses available width
  },
  modalButton: {
    backgroundColor: '#007AFF', // iOS blue, a common standard
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 25,
    elevation: 2,
    marginVertical: 5, // Consistent vertical margin
    minWidth: 130, 
    alignItems: 'center',
  },
  modalCloseButton: {
    backgroundColor: '#FF3B30', // iOS red for destructive/cancel actions
  },
  modalCancelButton: { // Specifically for cancel within a form-like modal
    backgroundColor: '#8E8E93', // iOS gray for secondary actions
  },
  modalButtonText: {
    color: 'white',
    fontWeight: '500', // Medium weight
    textAlign: 'center',
    fontSize: 16,
  },
  // Styles for GooglePlacesAutocomplete
  placesTextInputContainer: {
    backgroundColor: 'transparent',
    borderTopWidth: 0,
    borderBottomWidth: 0,
    width: '100%',
  },
  placesTextInput: {
    height: 45,
    borderColor: '#C7C7CD',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    backgroundColor: '#F9F9F9',
    marginBottom: 10, // Add some margin below the input
  },
  placesListView: {
    borderWidth: 1,
    borderColor: '#C7C7CD',
    borderRadius: 8,
    backgroundColor: 'white',
    maxHeight: 400, // Limit height of the results list
  },
  placesPoweredContainer: {
    display: 'none', // Hide "powered by Google"
  },
  renameInput: {
    height: 45,
    borderColor: '#C7C7CD',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 15, // Reduced margin
    width: '100%',
    fontSize: 16,
    backgroundColor: '#F9F9F9',
  },
  renameButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around', // Evenly space buttons
    width: '100%',
    marginTop: 10,
  },
});
