import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Alert, FlatList, Modal, Share, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { Polygon, usePolygons } from '../../contexts/PolygonContext';

export default function PolygonsScreen() {
  const { polygons, deletePolygon: deletePolygonFromContext, renamePolygon: renamePolygonFromContext, useMetricUnits, setPolygonToZoom } = usePolygons();
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [selectedPolygonForRename, setSelectedPolygonForRename] = useState<Polygon | null>(null);
  const [newPolygonName, setNewPolygonName] = useState('');
  const navigation = useNavigation();

  const handleGoToPolygon = (polygon: Polygon) => {
    setPolygonToZoom(polygon);
    navigation.navigate('index'); // Navigate to the Maps tab
  };

  const handleDeletePolygon = (id: string) => {
    Alert.alert(
      'Delete Polygon',
      'Are you sure you want to delete this polygon?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            deletePolygonFromContext(id);
          }
        },
      ]
    );
  };

  const sharePolygon = async (polygon: Polygon) => {
    try {
      const polygonToShare = {
        id: polygon.id,
        name: polygon.name,
        area: polygon.area,
        color: polygon.color,
        createdAt: polygon.createdAt instanceof Date ? polygon.createdAt.toISOString() : polygon.createdAt,
        coordinates: polygon.coordinates.map(coord => ({ latitude: coord.latitude, longitude: coord.longitude }))
      };
      const message = JSON.stringify(polygonToShare, null, 2);

      await Share.share({
        message,
        title: `Polygon: ${polygon.name}`,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share polygon');
    }
  };

  const openRenameModal = (polygon: Polygon) => {
    setSelectedPolygonForRename(polygon);
    setNewPolygonName(polygon.name);
    setIsRenameModalVisible(true);
  };

  const handleConfirmRename = () => {
    if (selectedPolygonForRename && newPolygonName.trim() !== '') {
      renamePolygonFromContext(selectedPolygonForRename.id, newPolygonName.trim());
      setIsRenameModalVisible(false);
      setSelectedPolygonForRename(null);
      setNewPolygonName('');
    } else {
      Alert.alert('Error', 'Polygon name cannot be empty.');
    }
  };

  const shareAllPolygons = async () => {
    if (polygons.length === 0) {
      Alert.alert('No Polygons', 'There are no polygons to share.');
      return;
    }
    try {
      const polygonsToShare = polygons.map(p => ({
        id: p.id,
        name: p.name,
        area: p.area,
        color: p.color,
        createdAt: p.createdAt instanceof Date ? p.createdAt.toISOString() : p.createdAt,
        coordinates: p.coordinates.map(coord => ({ latitude: coord.latitude, longitude: coord.longitude }))
      }));
      const message = JSON.stringify(polygonsToShare, null, 2); // Pretty print JSON

      await Share.share({
        message,
        title: 'All Polygons Data',
      });
    } catch (error) {
      Alert.alert('Error', `Failed to share all polygons: ${error}`);
    }
  };

  const renderPolygonItem = ({ item }: { item: Polygon }) => (
    <View style={styles.polygonItem}>
      <View style={styles.polygonHeader}>
        <View style={[styles.colorIndicator, { backgroundColor: item.color }]} />
        <Text style={styles.polygonName}>{item.name}</Text>
        <Text style={styles.polygonArea}>
          {useMetricUnits 
            ? `${item.area.toFixed(4)} ha` 
            : `${(item.area * 2.47105).toFixed(4)} acres`}
        </Text>
      </View>

      <View style={styles.polygonActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => openRenameModal(item)}
        >
          <MaterialCommunityIcons name="pencil" size={16} color="#2196F3" />
          <Text style={styles.actionText}>Rename</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => sharePolygon(item)}
        >
          <MaterialCommunityIcons name="share-variant" size={16} color="#4CAF50" />
          <Text style={styles.actionText}>Share</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeletePolygon(item.id)}
        >
          <MaterialCommunityIcons name="delete" size={16} color="#F44336" />
          <Text style={styles.actionText}>Delete</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleGoToPolygon(item)}
        >
          <MaterialCommunityIcons name="map-marker-radius" size={16} color="#FF9800" />
          <Text style={styles.actionText}>Goto</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      <View style={styles.header}>
        <Text style={styles.title}>Polygons</Text>
        <Text style={styles.subtitle}>{polygons.length} polygons</Text>
      </View>

      {polygons.length === 0 ? (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="vector-polygon" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No polygons yet</Text>
          <Text style={styles.emptySubtext}>Create polygons on the map screen</Text>
        </View>
      ) : (
        <FlatList
          data={polygons}
          renderItem={renderPolygonItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.polygonList}
        />
      )}

      {/* Share All Button */}
      {polygons.length > 0 && (
        <TouchableOpacity style={styles.shareAllButton} onPress={shareAllPolygons}>
          <MaterialCommunityIcons name="share-variant" size={24} color="white" />
          <Text style={styles.shareAllButtonText}>Share All Polygons</Text>
        </TouchableOpacity>
      )}

      {/* Rename Modal */}
      {selectedPolygonForRename && (
        <Modal
          animationType="fade"
          transparent={true}
          visible={isRenameModalVisible}
          onRequestClose={() => {
            setIsRenameModalVisible(false);
            setSelectedPolygonForRename(null);
          }}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalView}>
              <Text style={styles.modalTitle}>Rename Polygon</Text>
              <TextInput
                style={styles.renameInput}
                onChangeText={setNewPolygonName}
                value={newPolygonName}
                placeholder="Enter new polygon name"
              />
              <View style={styles.modalButtonsContainer}>
                <TouchableOpacity
                  style={styles.modalButton}
                  onPress={handleConfirmRename}
                >
                  <Text style={styles.modalButtonText}>Save</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.modalCancelButton]}
                  onPress={() => {
                    setIsRenameModalVisible(false);
                    setSelectedPolygonForRename(null);
                  }}
                >
                  <Text style={styles.modalButtonText}>Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  renameInput: {
    height: 40,
    borderColor: 'gray',
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginBottom: 20,
    width: '100%',
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  modalButton: {
    backgroundColor: '#2196F3',
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    minWidth: 100,
    alignItems: 'center',
  },
  modalCancelButton: {
    backgroundColor: '#f44336',
  },
  modalButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  shareAllButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    margin: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  shareAllButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Existing styles below
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    paddingTop: 60,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  polygonList: {
    padding: 16,
  },
  polygonItem: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  polygonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  polygonName: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  polygonArea: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  polygonActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 12,
  },
  actionButton: {
    paddingHorizontal: 5,
    paddingVertical: 5,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  actionText: {
    marginLeft: 3,
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Add new styles for the Goto button if needed
  // For example, if you want a different color or specific spacing
  gotoButton: {
    backgroundColor: '#FF9800',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#666',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
});
