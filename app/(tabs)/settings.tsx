import { MaterialCommunityIcons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, Switch, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { usePolygons } from '../../contexts/PolygonContext';

export default function SettingsScreen() {
  const { useMetricUnits, toggleUseMetricUnits, mapType, toggleMapType, googleMapsApiKey, setGoogleMapsApiKey } = usePolygons();//useContext(PolygonContext);
  const [apiKeyInput, setApiKeyInput] = useState(googleMapsApiKey || '');

  useEffect(() => {
    setApiKeyInput(googleMapsApiKey || '');
  }, [googleMapsApiKey]);

  const resetApp = () => {
    Alert.alert(
      'Reset App',
      'Are you sure you want to reset the app? This will delete all photos and polygons.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: () => {
            // In a real app, you would clear all data here
            Alert.alert('App Reset', 'All data has been cleared');
          }
        },
      ]
    );
  };

  const renderSettingItem = (icon: any, title: string, description: string, value: boolean, onToggle: () => void) => (
    <View style={styles.settingItem}>
      <MaterialCommunityIcons name={icon} size={24} color="#2196F3" style={styles.settingIcon} />
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: '#ccc', true: '#81c784' }}
        thumbColor={value ? '#4CAF50' : '#f4f3f4'}
      />
    </View>
  );

  const renderMapTypeSettingItem = (icon: any, title: string, description: string, currentMapType: string, onToggle: () => void) => (
    <View style={styles.settingItem}>
      <MaterialCommunityIcons name={icon} size={24} color="#2196F3" style={styles.settingIcon} />
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <Switch
        value={currentMapType === 'hybrid'} // True if hybrid, false if satellite
        onValueChange={onToggle}
        trackColor={{ false: '#ccc', true: '#81c784' }}
        thumbColor={currentMapType === 'hybrid' ? '#4CAF50' : '#f4f3f4'}
      />
    </View>
  );

  const renderActionItem = (icon: any, title: string, description: string, onPress: () => void, color: string = '#2196F3') => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <MaterialCommunityIcons name={icon} size={24} color={color} style={styles.settingIcon} />
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <MaterialCommunityIcons name="chevron-right" size={24} color="#999" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Map Settings</Text>
          {renderSettingItem(
            'ruler',
            'Use Metric Units',
            'Display measurements in feet-acres/meters-hectares',
            useMetricUnits,
            toggleUseMetricUnits
          )}
          {renderMapTypeSettingItem(
            'map',
            'Map Type',
            'Switch between Satellite/Hybrid map views',
            mapType,
            toggleMapType
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>API Keys</Text>
          <View style={styles.settingItem}>
            <MaterialCommunityIcons name="key-variant" size={24} color="#2196F3" style={styles.settingIcon} />
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Google Maps API Key</Text>
              <Text style={styles.settingDescription}>Enter Your OWN Google Maps Place API Key(Optional, Stored)</Text>
              <TextInput
                style={styles.input}
                placeholder="Google Maps API Key"
                value={apiKeyInput}
                onChangeText={setApiKeyInput}
                onSubmitEditing={() => setGoogleMapsApiKey(apiKeyInput.trim() === '' ? null : apiKeyInput.trim())}
                onBlur={() => setGoogleMapsApiKey(apiKeyInput.trim() === '' ? null : apiKeyInput.trim())} // Save on blur as well
              />
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          {renderActionItem(
            'delete',
            'Reset App',
            'Delete all data and reset the app',
            resetApp,
            '#F44336'
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          {renderActionItem(
            'information',
            'About MapPond',
            'Version 1.0.0',
            () => Alert.alert('About', 'MapPond v1.0.0\nDeveloped with React Native and Expo')
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    paddingTop: 60,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    marginHorizontal: 16,
    marginBottom: 8,
    marginTop: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingIcon: {
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
    fontSize: 16,
  },
});
